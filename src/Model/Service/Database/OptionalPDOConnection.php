<?php

namespace App\Model\Service\Database;

use PDO;
use PDOException;
use App\Model\Api\Logger\LoggerInterface;
use App\Model\Api\Logger\Constants\LoggerTopic;
use App\Model\Api\Logger\Constants\LoggerKey;

/**
 * Optional PDO Connection that gracefully handles connection failures
 */
class OptionalPDOConnection
{
    private ?PDO $connection = null;
    private bool $connectionAttempted = false;
    private bool $connectionAvailable = false;

    private string $dsn;
    private string $username;
    private string $password;
    private array $options;
    private ?LoggerInterface $logger;

    public function __construct(
        string $dsn,
        string $username,
        string $password,
        array $options = [],
        LoggerInterface $logger = null
    ) {
        $this->dsn = $dsn;
        $this->username = $username;
        $this->password = $password;
        $this->options = $options;
        $this->logger = $logger;
    }

    /**
     * Get the PDO connection if available, null otherwise
     */
    public function getConnection(): ?PDO
    {
        if (!$this->connectionAttempted) {
            $this->attemptConnection();
        }

        return $this->connectionAvailable ? $this->connection : null;
    }

    /**
     * Check if connection is available
     */
    public function isAvailable(): bool
    {
        if (!$this->connectionAttempted) {
            $this->attemptConnection();
        }

        return $this->connectionAvailable;
    }

    /**
     * Attempt to establish the connection
     */
    private function attemptConnection(): void
    {
        $this->connectionAttempted = true;

        try {
            $this->connection = new PDO($this->dsn, $this->username, $this->password, $this->options);
            $this->connectionAvailable = true;

            if ($this->logger) {
                $this->logger->info(LoggerTopic::DATABASE, LoggerKey::ACCESS, 'Optional PDO connection established successfully: ' . $this->dsn);
            }
        } catch (PDOException $e) {
            $this->connectionAvailable = false;
            $this->connection = null;

            if ($this->logger) {
                $this->logger->warning(LoggerTopic::DATABASE, LoggerKey::ACCESS, 'Optional PDO connection failed, service will be disabled: ' . $this->dsn . ' - ' . $e->getMessage());
            }
        }
    }

    /**
     * Magic method to proxy PDO methods when connection is available
     */
    public function __call(string $method, array $arguments)
    {
        $connection = $this->getConnection();

        if ($connection === null) {
            throw new \RuntimeException('Database connection is not available');
        }

        return call_user_func_array([$connection, $method], $arguments);
    }

    /**
     * Reset connection state (useful for testing or reconnection)
     */
    public function reset(): void
    {
        $this->connection = null;
        $this->connectionAttempted = false;
        $this->connectionAvailable = false;
    }
}