<?php

/**
 * E<PERSON>eller: <PERSON>
 * E-Mail: <EMAIL>
 * Datum: 04.12.17 07:15.
 */

namespace App\Model\Api\IssueTracker;

use App\Exception\GitlabException;
use App\Model\Api\Facade\cURLFacade;
use App\Model\Api\Facade\DateTimeFacade;
use App\Model\Api\FileOperation\FileHandler;
use App\Model\Api\Logger\Constants\LoggerKey;
use App\Model\Api\Logger\Constants\LoggerTopic;
use App\Model\Api\Logger\Constants\LoggerValue;
use App\Model\Api\Logger\LoggerInterface;
use App\Model\Api\PortalUser\PortalUserInterface;
use League\HTMLToMarkdown\HtmlConverter;
use Symfony\Component\HttpFoundation\Response;

class IssueTrackerGitlab implements IssueTrackerInterface
{
    /** @var PortalUserInterface */
    private $portalUser;

    /** @var HtmlConverter */
    private $htmlConverter;

    /** @var FileHandler */
    private $fileHandler;

    /** @var LoggerInterface */
    private $logger;

    /** @var string */
    private $baseUploadPath;

    /** @var cURLFacade */
    private $cURLFacade;

    /** @var DateTimeFacade */
    private $dateTimeFacade;

    /** @var array */
    private $gitlabParameters;

    /** @var string */
    private $gitlabToken;

    /** @var string */
    private $gitlabURL;

    /** @var string */
    private $gitlabUploadDirectory;

    /** @var string */
    private $type;

    /** @var string */
    private $service;

    /** @var string */
    private $description;

    /** @var string */
    private $descriptionAsMarkdown;

    /** @var string */
    private $os;

    /** @var string */
    private $browser;

    /** @var string */
    private $browserVersion;

    /**
     * Gitlab constructor.
     */
    public function __construct(PortalUserInterface $portalUserInterface, HtmlConverter $htmlConverter, FileHandler $fileHandler, LoggerInterface $loggerInterface, cURLFacade $cURLFacade, DateTimeFacade $dateTime, array $gitlabParameters, string $baseUploadPath)
    {
        $this->portalUser = $portalUserInterface;

        $this->portalUser->fetch();

        $this->htmlConverter = $htmlConverter;
        $this->fileHandler = $fileHandler;
        $this->logger = $loggerInterface;
        $this->cURLFacade = $cURLFacade;
        $this->dateTimeFacade = $dateTime;
        $this->gitlabParameters = $gitlabParameters;
        $this->baseUploadPath = $baseUploadPath;

        $this->gitlabUploadDirectory = $this->gitlabParameters['upload_directory'];
        $this->gitlabToken = $this->gitlabParameters['private_token'];
        $this->gitlabURL = $this->gitlabParameters['url'];
    }

    /**
     * Erzeugt ein neues Issue in Gitlab.
     *
     * @throws GitlabException
     * @throws \Exception
     */
    public function createIssue(array $postData): Response
    {
        $this->sanitizePostData($postData);
        $this->dataComplete();
        $this->descriptionToMarkdown();

        /*
         * Werden im Markdown Bilder gefunden, müssen diese extrahiert und gesondert ans Gitlab gesendet werden
         * Die von Gitlab zurückgegebenen URLs müssen dann die ursprünglichen URLs ersetzen
         */
        $matches = [];
        preg_match_all('/!\[[^\]]*\]\(\/reportProblem\/image\/([^\s\)"]*)\)/', $this->descriptionAsMarkdown, $matches);

        $this->replaceMarkdownImages($this->uploadImagesToGitlab($matches));

        $this->sendIssueToGitlab();

        return new Response();
    }

    /**
     * Ersetzt die Images im Markdown von Summernote durch die ersetzten im Gitlab.
     */
    private function replaceMarkdownImages(array $uploadedMarkdown): void
    {
        foreach ($uploadedMarkdown as $markdown) {
            $this->descriptionAsMarkdown = str_replace($markdown['originalMarkdown'], $markdown['gitlabMarkdown'], $this->descriptionAsMarkdown);
        }
    }

    /**
     * Löscht eine übertragene Datei auf dem Server.
     *
     * @throws \App\Exception\FileHandlerValuesMissingException
     */
    private function deleteUploadedImage(string $relativeFilePath)
    {
        $this->fileHandler->setPrivate(true);
        $this->fileHandler->setPermission($this->portalUser->isLoggedIn());
        $this->fileHandler->remove($relativeFilePath);
    }

    /**
     * Wandelt die Description von HTML zu Markdown.
     */
    private function descriptionToMarkdown(): void
    {
        $this->descriptionAsMarkdown = $this->htmlConverter->convert(htmlspecialchars_decode($this->description));
    }

    /**
     * Escaped das POST Array.
     */
    private function sanitizePostData(array $postData): void
    {
        isset($postData['type']) ? $this->type = filter_var($postData['type'], \FILTER_SANITIZE_STRING) : $this->type = null;
        isset($postData['service']) ? $this->service = filter_var($postData['service'], \FILTER_SANITIZE_STRING) : $this->service = null;
        isset($postData['description']) ? $this->description = filter_var(htmlspecialchars($postData['description']), \FILTER_SANITIZE_STRING) : $this->description = null;
        isset($postData['os']) ? $this->os = filter_var($postData['os'], \FILTER_SANITIZE_STRING) : $this->os = null;
        isset($postData['browser']) ? $this->browser = filter_var($postData['browser'], \FILTER_SANITIZE_STRING) : $this->browser = null;
        isset($postData['browserVersion']) ? $this->browserVersion = filter_var($postData['browserVersion'], \FILTER_SANITIZE_STRING) : $this->browserVersion = null;
    }

    /**
     * Überprüft ob alle nötigen Daten vorhanden sind.
     *
     * @throws GitlabException
     */
    private function dataComplete(): void
    {
        if (null === $this->type || null === $this->service || null === $this->description) {
            throw new GitlabException('Provided data incomplete.');
        }
    }

    /**
     * Lädt alle Images im Markdown vom Summernote ins Gitlab System und gibt die entsprechenden URLs von Gitlab zurück.
     *
     * @throws GitlabException
     * @throws \App\Exception\FileHandlerValuesMissingException
     */
    private function uploadImagesToGitlab(array $images): array
    {
        $******************** = [];

        // Wurde ein gültiges Array übergeben?
        if (isset($images[0]) && isset($images[1])) {
            $url = $this->gitlabURL.$this->gitlabParameters['projects_subfolder'].$this->gitlabParameters['projectid'].'/uploads';

            for ($i = 0; $i < \count($images[0]); ++$i) {
                $data = [
                    'file' => $this->cURLFacade->curl_file_create($this->baseUploadPath.$this->gitlabUploadDirectory.$images[1][$i]),
                ];

                $result = $this->curl($url, $data);

                $this->deleteUploadedImage($this->gitlabUploadDirectory.$images[1][$i]);

                $********************[] = [
                    'originalMarkdown' => $images[0][$i],
                    'gitlabMarkdown' => $result['markdown'],
                ];
            }
        }

        return $********************;
    }

    /**
     * Dateien per cURL an Gitlab senden.
     */
    private function sendIssueToGitlab(): void
    {
        $url = $this->gitlabURL.$this->gitlabParameters['projects_subfolder'].$this->gitlabParameters['projectid'].'/issues';

        $footer = "___\n\n";
        $footer .= 'Name: '.$this->portalUser->getLastName().', '.$this->portalUser->getFirstName()."\n\n";
        $footer .= 'Company: '.$this->portalUser->getCompany()."\n\n";
        $footer .= 'eMail: ['.$this->portalUser->getUser()->getEmail().'](mailto:'.$this->portalUser->getUser()->getEmail().")\n\n";
        $footer .= "Operation System: $this->os \n\n";
        $footer .= "Browser: $this->browser ($this->browserVersion)\n\n";

        $data = [
            'title' => $this->gitlabParameters['title'].' '.$this->service,
            'description' => $this->descriptionAsMarkdown."\n\n".$footer,
            'created_at' => $this->dateTimeFacade->get()->format(\DateTime::ATOM),
            'labels' => 'reported by portal,'.$this->gitlabParameters['labels'][$this->type],
        ];

        $this->curl($url, $data);
    }

    /**
     * Sendet Daten per cURL an Gitlab.
     *
     * @throws GitlabException
     */
    private function curl(string $url, array $data): array
    {
        try {
            $ch = $this->cURLFacade->curl_init();

            $this->cURLFacade->curl_setopt($ch, \CURLOPT_URL, $url);
            $this->cURLFacade->curl_setopt($ch, \CURLOPT_HTTPHEADER, ['PRIVATE-TOKEN:'.$this->gitlabToken]);
            $this->cURLFacade->curl_setopt($ch, \CURLOPT_POST, true);
            $this->cURLFacade->curl_setopt($ch, \CURLOPT_POSTFIELDS, $data);
            $this->cURLFacade->curl_setopt($ch, \CURLOPT_RETURNTRANSFER, true);
            $this->cURLFacade->curl_setopt($ch, \CURLOPT_FOLLOWLOCATION, 1);

            // In den Entwicklungsumgebungen muss mal wieder der Proxy dazwischen
            if (strstr($_ENV['DOMAIN'], 'development')) {
                $this->cURLFacade->curl_setopt($ch, \CURLOPT_PROXY, $this->gitlabParameters['proxyhost']);
            }

            $result = $this->cURLFacade->curl_exec($ch);

            if (false === $result) {
                throw new GitlabException('cURL to Gitlab not successful');
            }

            $this->cURLFacade->curl_close($ch);

            return json_decode($result, true);
        } catch (\Exception|GitlabException $e) {
            $this->cURLFacade->curl_close($ch);
            $this->logger->error(LoggerTopic::USERACTION, LoggerKey::GITLAB, LoggerValue::FAILED, ['url' => $url, 'data' => $data, 'Exception' => 'cURL to Gitlab not successful: '.$e->getMessage()]);
            throw new GitlabException('cURL to Gitlab not successful');
        }
    }
}
