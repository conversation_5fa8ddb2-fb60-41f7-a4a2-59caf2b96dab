<?php

/**
 * Ersteller: <PERSON>
 * E-Mail: konstantin,<EMAIL>
 * Datum: 08.05.2025.
 */

namespace App\Model\Api\IssueTracker;

use App\Model\Api\Logger\Constants\LoggerKey;
use App\Model\Api\Logger\Constants\LoggerTopic;
use App\Model\Api\Logger\Constants\LoggerValue;
use App\Model\Api\Logger\LoggerInterface;
use App\Model\Api\PortalUser\PortalUserInterface;
use Asana\Client;
use Asana\Errors\NotFoundError;
use Doctrine\DBAL\Exception;
use League\HTMLToMarkdown\HtmlConverter;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class IssueTrackerAsana implements IssueTrackerInterface
{
    private PortalUserInterface $portalUser;
    private HtmlConverter $htmlConverter;
    private LoggerInterface $logger;
    private string $type;
    private string $service;
    private string $description;
    private string $descriptionAsMarkdown;
    private string $os;
    private string $browser;
    private string $browserVersion;
    private array $asanaParameters;
    private string $projectId;
    private Client $client;
    private array $ticketImages;

    public function __construct(PortalUserInterface $portalUserInterface, HtmlConverter $htmlConverter, LoggerInterface $loggerInterface, array $asanaParameters)
    {
        $this->portalUser = $portalUserInterface;
        $this->portalUser->fetch();
        $this->htmlConverter = $htmlConverter;
        $this->logger = $loggerInterface;
        $this->asanaParameters = $asanaParameters;
        $this->projectId = 1210170022589801;
        $this->client = Client::accessToken($this->asanaParameters['personal_token'], [
            'headers' => [ // https://developers.asana.com/docs/deprecations
                'asana-enable' => 'new_user_task_lists,new_project_templates,new_memberships,new_goal_memberships',
            ],
        ]);
    }

    /**
     * Erzeugt ein neues Issue in Asana und hängt die Bilder an.
     *
     * @throws \Exception
     */
    public function createIssue(array $postData): Response
    {
        $this->sanitizePostData($postData);
        $this->descriptionToMarkdown();

        /*
         * Werden im Markdown Bilder gefunden, müssen diese extrahiert und gesondert ans Ticket gesendet werden
         * Die von Gitlab zurückgegebenen URLs müssen dann die ursprünglichen URLs ersetzen
         */
        $matches = [];
        preg_match_all('/([\s\S]*?)!\[[^]]*]\(https?:\/\/[^\/]+\/reportProblem\/image\/([^\/\s)"]+)\)/', $this->descriptionAsMarkdown, $matches);

        if (!empty($matches[2])) {
            $this->descriptionAsMarkdown = $matches[1][0];
            $this->ticketImages = $matches[2];
        }

        $success = $this->sendIssueToAsana();

        if ($success) {
            return new Response();
        } else {
            return new JsonResponse(['message' => 'Reporting failed'], 500);
        }
    }

    /**
     * Wandelt die Description von HTML zu Markdown um.
     */
    private function descriptionToMarkdown(): void
    {
        $this->descriptionAsMarkdown = $this->htmlConverter->convert(htmlspecialchars_decode($this->description));
    }

    /**
     * Escaped das POST Array.
     */
    private function sanitizePostData(array $postData): void
    {
        isset($postData['type']) ? $this->type = filter_var($postData['type'], \FILTER_SANITIZE_FULL_SPECIAL_CHARS) : $this->type = null;
        isset($postData['service']) ? $this->service = filter_var($postData['service'], \FILTER_SANITIZE_FULL_SPECIAL_CHARS) : $this->service = null;
        isset($postData['description']) ? $this->description = filter_var(htmlspecialchars($postData['description']), \FILTER_SANITIZE_FULL_SPECIAL_CHARS) : $this->description = null;
        isset($postData['os']) ? $this->os = filter_var($postData['os'], \FILTER_SANITIZE_FULL_SPECIAL_CHARS) : $this->os = null;
        isset($postData['browser']) ? $this->browser = filter_var($postData['browser'], \FILTER_SANITIZE_FULL_SPECIAL_CHARS) : $this->browser = null;
        isset($postData['browserVersion']) ? $this->browserVersion = filter_var($postData['browserVersion'], \FILTER_SANITIZE_FULL_SPECIAL_CHARS) : $this->browserVersion = null;
    }

    /**
     * Issue per REST an Asana senden.
     */
    private function sendIssueToAsana(): bool
    {
        $footer = "___\n\n";
        $footer .= 'Name: '.$this->portalUser->getLastName().', '.$this->portalUser->getFirstName()."\n\n";
        $footer .= 'Company: '.$this->portalUser->getCompany()."\n\n";
        $footer .= 'eMail: ['.$this->portalUser->getUser()->getEmail().'](mailto:'.$this->portalUser->getUser()->getEmail().")\n\n";
        $footer .= "Operation System: $this->os \n\n";
        $footer .= "Browser: $this->browser ($this->browserVersion)\n\n";

        $data = [
            'workspace' => $this->asanaParameters['workspace_id'].'',
            'projects' => [$this->projectId],
            'name' => 'Portal Report: '.$this->type.'/'.$this->service,
            'notes' => $this->descriptionAsMarkdown."\n\n".$footer,
        ];

        $asanaUserGid = $this->getAsanaUser();
        if ($asanaUserGid > 0) {
            $data['followers'][] = $asanaUserGid.'';
        }

        return $this->createTask($data);
    }

    /**
     * Creates a new task in Asana.
     */
    private function createTask($data): bool
    {
        try {
            $result = $this->client->tasks->createTask($data, ['opt_pretty' => 'true']);

            return $this->addImagesToTask($result->gid);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Uploads images to an existing task.
     */
    private function addImagesToTask($taskId): bool
    {
        if (empty($this->ticketImages)) {
            return true;
        }
        foreach ($this->ticketImages as $image) {
            $imagePath = $this->asanaParameters['task_image_upload_directory'].$image;
            // Dateiinhalt laden
            $content = file_get_contents($imagePath);
            if (false === $content) {
                continue;
            }
            $filename = basename($imagePath);
            $contentType = mime_content_type($imagePath);

            try {
                // Attachment über den Asana-Client hochladen
                $this->client->attachments->createOnTask($taskId, $content, $filename, $contentType);
                unlink($imagePath);
            } catch (\Exception $e) {
                $this->logger->error(LoggerTopic::USERACTION, LoggerKey::ASANA, LoggerValue::FAILED, ['Image' => $imagePath, 'Exception' => 'Image upload to Asana failed: '.$e->getMessage()]);

                return false;
            }
        }

        return true;
    }

    /**
     * Retrieves the Asana user ID (gid) associated with the email of the current portal user.
     *
     * @return int the Asana user ID if found, or 0 if the user is not found
     */
    private function getAsanaUser(): int
    {
        try {
            $result = $this->client->users->getUser($this->portalUser->getUser()->getEmail());

            return $result->gid;
        } catch (NotFoundError $e) {
            return 0;
        }
    }
}
